using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Oxide.Core;
using Oxide.Core.Plugins;
using UnityEngine;
using Newtonsoft.Json;

namespace Oxide.Plugins
{
    [Info("The Den Anti Cheat", "Golgolak", "2.4.0")]
    [Description("Advanced cheat detection with dynamic weapon data integration for PvP scenarios")]
    class TheDenAntiCheat : RustPlugin
    {
        private Dictionary<ulong, PlayerData> playerData = new Dictionary<ulong, PlayerData>();
        private string currentDate; // Track current date for log file
        private static readonly DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);

        // Violation Stats System
        private Dictionary<ulong, ViolationStats> violationStatsCache = new Dictionary<ulong, ViolationStats>();
        private bool violationStatsDirty = false;
        private DateTime lastViolationStatsSave = DateTime.UtcNow;

        // Encounter Tracking System
        private Dictionary<string, EncounterData> activeEncounters = new Dictionary<string, EncounterData>();

        // Detection Logging System (replaces violation log and recoil debug)
        private List<string> detectionLogBuffer = new List<string>();
        private Timer detectionLogFlushTimer;
        private string detectionLogPath;

        // Admin Stats System
        private AdminStats adminStats = new AdminStats();
        private DateTime pluginStartTime = DateTime.UtcNow;
        private bool adminStatsDirty = false;

        #region Weapon Data
        private Dictionary<string, WeaponData> weaponDatabase = new Dictionary<string, WeaponData>();
        private DateTime lastWeaponDataLoad = DateTime.MinValue;
        private string weaponDataPath = "oxide/data/thedenproject/weapon_dump.json";

        private class WeaponData
        {
            public string DisplayName { get; set; }
            public string ShortName { get; set; }
            public int ItemID { get; set; }
            public float YawMin { get; set; }
            public float YawMax { get; set; }
            public float PitchMin { get; set; }
            public float PitchMax { get; set; }
            public float Aimcone { get; set; }
            public int RPM { get; set; }
            public bool IsAutomatic { get; set; }
            public string AmmoType { get; set; }
            public int MagazineSize { get; set; }
            public float ReloadTime { get; set; }
            
            // Calculated properties
            public float FireInterval => RPM > 0 ? 60f / RPM : 1f;
            public float ExpectedRecoilRange => Mathf.Abs(YawMax - YawMin) + Mathf.Abs(PitchMax - PitchMin);
        }
        #endregion

        #region Configuration
        private class ConfigData
        {
            public GeneralSettings General { get; set; } = new GeneralSettings();
            public AimbotSettings Aimbot { get; set; } = new AimbotSettings();
            public RecoilSettings Recoil { get; set; } = new RecoilSettings();
            public LoggingSettings Logging { get; set; } = new LoggingSettings();
        }

        private class GeneralSettings
        {
            public bool EnableAimbotDetection { get; set; } = true;
            public bool EnableRecoilDetection { get; set; } = true;
            public bool EnableWallhackDetection { get; set; } = true;
            public bool EnableViolationStats { get; set; } = true; // Track violation statistics for repeat offenders
            public bool DebugMode { get; set; } = false; // Debug mode: detect all combat (not just PvP)
        }

        private class AimbotSettings
        {
            public float MaxHitRatio { get; set; } = 0.95f; // Hit ratio threshold
            public int MinShots { get; set; } = 10; // Minimum shots to analyze
        }

        private class RecoilSettings
        {
            public float MaxAngleChange { get; set; } = 0.1f; // Max aim angle change per shot (legacy)
            public float VarianceThreshold { get; set; } = 0.3f; // Minimum acceptable variance: 0.3 = player must have at least 30% of weapon's natural recoil variance
            public bool EnablePatternDetection { get; set; } = true; // Enable pattern-based detection
            public float EventTimeout { get; set; } = 60f; // Encounter timeout in seconds
        }

        private class LoggingSettings
        {
            public bool EnableLogging { get; set; } = true; // Enable/disable CSV logging
            public bool EnableConsoleLogging { get; set; } = false; // Enable/disable console logging
            public float SaveFrequency { get; set; } = 300f; // Log write interval (seconds)
            public float MaxBufferSizeKB { get; set; } = 256f; // Buffer size limit (KB)
        }

        private ConfigData config;

        protected override void LoadConfig()
        {
            base.LoadConfig();
            
            if (!Config.Exists())
            {
                // No config file exists - create new with defaults
                Puts("Creating new configuration file with default settings...");
                config = GetDefaultConfig();
                SaveConfig();
                return;
            }
            
            // Load existing config
            config = Config.ReadObject<ConfigData>();
            
            // Get default config for comparison
            var defaultConfig = GetDefaultConfig();
            
            // Merge any missing settings with defaults (preserving existing user settings)
            bool configChanged = MergeConfigSettings(config, defaultConfig);
            
            // Save config if any new settings were added
            if (configChanged)
            {
                Puts("Configuration updated with new settings while preserving your existing settings.");
                SaveConfig();
            }
        }

        private bool MergeConfigSettings(ConfigData userConfig, ConfigData defaultConfig)
        {
            bool hasChanges = false;
            
            // Ensure all sections exist - add missing sections with defaults
            if (userConfig.General == null)
            {
                userConfig.General = defaultConfig.General;
                hasChanges = true;
            }
            if (userConfig.Aimbot == null)
            {
                userConfig.Aimbot = defaultConfig.Aimbot;
                hasChanges = true;
            }
            if (userConfig.Recoil == null)
            {
                userConfig.Recoil = defaultConfig.Recoil;
                hasChanges = true;
            }
            if (userConfig.Logging == null)
            {
                userConfig.Logging = defaultConfig.Logging;
                hasChanges = true;
            }
            
            // Clean up deprecated settings and add missing ones within each section
            // This uses reflection to compare properties and clean up obsolete ones
            hasChanges |= CleanAndMergeSection(userConfig.General, defaultConfig.General);
            hasChanges |= CleanAndMergeSection(userConfig.Aimbot, defaultConfig.Aimbot);
            hasChanges |= CleanAndMergeSection(userConfig.Recoil, defaultConfig.Recoil);
            hasChanges |= CleanAndMergeSection(userConfig.Logging, defaultConfig.Logging);
            
            return hasChanges;
        }

        private bool CleanAndMergeSection<T>(T userSection, T defaultSection) where T : class
        {
            bool hasChanges = false;
            
            if (userSection == null || defaultSection == null)
                return false;
            
            var userType = userSection.GetType();
            var defaultType = defaultSection.GetType();
            
            // Get all properties from both user and default configs
            var userProperties = userType.GetProperties().ToDictionary(p => p.Name, p => p);
            var defaultProperties = defaultType.GetProperties().ToDictionary(p => p.Name, p => p);
            
            // Add missing properties from default config (new settings)
            foreach (var defaultProp in defaultProperties.Values)
            {
                if (!userProperties.ContainsKey(defaultProp.Name))
                {
                    // Property exists in default but not in user config - add it
                    if (defaultProp.CanWrite)
                    {
                        var defaultValue = defaultProp.GetValue(defaultSection);
                        defaultProp.SetValue(userSection, defaultValue);
                        hasChanges = true;
                    }
                }
            }
            
            // Remove obsolete properties (properties that exist in user config but not in default)
            // Note: This is more complex with reflection and C# objects, so we'll handle this
            // by reconstructing the object with only valid properties
            if (userProperties.Count != defaultProperties.Count)
            {
                // There are extra properties in user config that don't exist in default
                // We need to clean them up by copying only valid properties to a new instance
                var cleanSection = Activator.CreateInstance<T>();
                
                foreach (var defaultProp in defaultProperties.Values)
                {
                    if (userProperties.ContainsKey(defaultProp.Name) && defaultProp.CanWrite && defaultProp.CanRead)
                    {
                        // Copy valid property from user config
                        var userValue = defaultProp.GetValue(userSection);
                        defaultProp.SetValue(cleanSection, userValue);
                    }
                    else if (defaultProp.CanWrite && defaultProp.CanRead)
                    {
                        // Use default value for missing properties
                        var defaultValue = defaultProp.GetValue(defaultSection);
                        defaultProp.SetValue(cleanSection, defaultValue);
                    }
                }
                
                // Copy clean properties back to user section
                foreach (var prop in defaultProperties.Values)
                {
                    if (prop.CanWrite && prop.CanRead)
                    {
                        var cleanValue = prop.GetValue(cleanSection);
                        prop.SetValue(userSection, cleanValue);
                    }
                }
                
                hasChanges = true;
            }
            
            return hasChanges;
        }

        protected override void LoadDefaultConfig() => config = GetDefaultConfig();

        private ConfigData GetDefaultConfig()
        {
            return new ConfigData
            {
                General = new GeneralSettings
                {
                    EnableAimbotDetection = true,
                    EnableRecoilDetection = true,
                    EnableWallhackDetection = true,
                    EnableViolationStats = true,
                    DebugMode = false
                },
                Aimbot = new AimbotSettings
                {
                    MaxHitRatio = 0.85f,
                    MinShots = 10
                },
                Recoil = new RecoilSettings
                {
                    MaxAngleChange = 0.1f,
                    VarianceThreshold = 0.3f,
                    EnablePatternDetection = true,
                    EventTimeout = 60f
                },
                Logging = new LoggingSettings
                {
                    EnableLogging = true,
                    EnableConsoleLogging = false,
                    SaveFrequency = 300f,
                    MaxBufferSizeKB = 256f
                }
            };
        }

        protected override void SaveConfig() => Config.WriteObject(config, true);
        #endregion

        #region Player Data
        private class PlayerData
        {
            public int ShotsFired { get; set; }
            public int ShotsHit { get; set; }
            public List<ShotData> ShotHistory { get; set; } = new List<ShotData>();
            public List<Violation> Violations { get; set; } = new List<Violation>();
            public Vector3 LastAimAngle { get; set; } = Vector3.zero; // Track previous aim angle
        }

        private class ShotData
        {
            public Vector3 AimAngleBefore { get; set; }
            public Vector3 AimAngleAfter { get; set; }
            public float Time { get; set; }
            public Vector3 FromPos { get; set; }
            public Vector3 HitPos { get; set; }
            public string WeaponShortName { get; set; }
            public bool IsValidRecoilData { get; set; } = false; // Flag for valid angle data
        }

        private class Violation
        {
            public string Type { get; set; }
            public double Time { get; set; }
            public string Weapon { get; set; }
            public string Details { get; set; }
        }

        private class ViolationStats
        {
            public string PlayerName { get; set; }
            public int TotalViolations { get; set; }
            public Dictionary<string, int> ViolationsByType { get; set; } = new Dictionary<string, int>();
            public DateTime FirstViolation { get; set; }
            public DateTime LastViolation { get; set; }
            
            // Daily stats (reset each day)
            public int ViolationsToday { get; set; } = 0;
            public Dictionary<string, int> ViolationsByTypeToday { get; set; } = new Dictionary<string, int>();
            public DateTime CurrentStatsDate { get; set; } = DateTime.UtcNow.Date;
        }

        // Encounter-based detection data structure
        private class EncounterData
        {
            public string EncounterID { get; set; }
            public ulong AttackerID { get; set; }
            public ulong TargetID { get; set; }
            public DateTime StartTime { get; set; }
            public DateTime LastShotTime { get; set; }
            public List<ShotData> Shots { get; set; } = new List<ShotData>();
            public Timer TimeoutTimer { get; set; }
            public string PrimaryWeapon { get; set; } = ""; // Most used weapon in encounter
            public Dictionary<string, int> WeaponUsage { get; set; } = new Dictionary<string, int>(); // Track weapon usage
        }

        // Detection entry for unified logging
        private class DetectionEntry
        {
            public string Timestamp { get; set; }
            public string EncounterID { get; set; }
            public string PlayerID { get; set; }
            public string PlayerName { get; set; }
            public string TargetID { get; set; }
            public string TargetName { get; set; }
            public string ViolationType { get; set; }
            public string Weapon { get; set; }
            public float EncounterDuration { get; set; }
            public int ShotCount { get; set; }
            public bool WasDetected { get; set; }

            // Weapon database fields
            public bool WeaponHasData { get; set; }
            public float WeaponExpectedRecoilRange { get; set; }
            public float WeaponFireInterval { get; set; }
            public int WeaponRPM { get; set; }
            public string WeaponDisplayName { get; set; }
            public float WeaponYawMin { get; set; }
            public float WeaponYawMax { get; set; }
            public float WeaponPitchMin { get; set; }
            public float WeaponPitchMax { get; set; }

            // Configuration fields
            public bool PatternDetectionEnabled { get; set; }
            public float ConfigVarianceThreshold { get; set; }
            public float ConfigMaxAngleChange { get; set; }

            // Detection analysis fields
            public string DetectionMethod { get; set; }
            public string AngleChangesList { get; set; }
            public float CalculatedMean { get; set; }
            public float CalculatedVariance { get; set; }
            public float CalculatedStdDev { get; set; }
            public float CalculatedThreshold { get; set; }
            public float PlayerRecoilRange { get; set; }
            public float PlayerMinRecoil { get; set; }
            public float PlayerMaxRecoil { get; set; }
            public float DeviationPercentage { get; set; }

            private string SafeReplace(string input)
            {
                return input?.Replace(",", ";") ?? "";
            }

            public string ToCSV()
            {
                return $"{Timestamp ?? ""},{EncounterID ?? ""},{PlayerID ?? ""},{SafeReplace(PlayerName)}," +
                       $"{TargetID ?? ""},{SafeReplace(TargetName)}," +
                       $"{ViolationType ?? ""},{Weapon ?? ""}," +
                       $"{EncounterDuration:F2},{ShotCount},{WasDetected}," +
                       $"{WeaponHasData},{WeaponExpectedRecoilRange:F3},{WeaponFireInterval:F3}," +
                       $"{WeaponRPM},{SafeReplace(WeaponDisplayName)}," +
                       $"{WeaponYawMin:F3},{WeaponYawMax:F3},{WeaponPitchMin:F3},{WeaponPitchMax:F3}," +
                       $"{PatternDetectionEnabled},{ConfigVarianceThreshold:F3},{ConfigMaxAngleChange:F3}," +
                       $"{DetectionMethod ?? ""},{SafeReplace(AngleChangesList)}," +
                       $"{CalculatedMean:F3},{CalculatedVariance:F3},{CalculatedStdDev:F3}," +
                       $"{CalculatedThreshold:F3},{PlayerRecoilRange:F3},{PlayerMinRecoil:F3}," +
                       $"{PlayerMaxRecoil:F3},{DeviationPercentage:F1}";
            }
        }

        // Admin Stats System
        private class AdminStats
        {
            public string LastUpdated { get; set; } = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");
            public AdminStatsCategory AllTime { get; set; } = new AdminStatsCategory();
            public AdminStatsCategory Daily { get; set; } = new AdminStatsCategory();
            public AdminStatsCategory Weekly { get; set; } = new AdminStatsCategory();
            public AdminStatsRealtime Realtime { get; set; } = new AdminStatsRealtime();
        }

        private class AdminStatsCategory
        {
            public DateTime Date { get; set; } = DateTime.UtcNow.Date;
            public ShotRecord MaxShotsPerEncounter { get; set; } = new ShotRecord();
            public BufferRecord MaxBufferSize { get; set; } = new BufferRecord();
            public int TotalEncounters { get; set; } = 0;
            public int MaxConcurrentEncounters { get; set; } = 0;
            public float LargestLogWriteKB { get; set; } = 0f;
            public int StaleEncounterWarnings { get; set; } = 0;
            public float LongestEncounterSeconds { get; set; } = 0f;
        }

        private class AdminStatsRealtime
        {
            public int ActiveEncounters { get; set; } = 0;
            public float EncountersPerHour { get; set; } = 0f;
            public float DetectionRatePercent { get; set; } = 0f;
            public int FlushOperationsToday { get; set; } = 0;
        }

        private class ShotRecord
        {
            public int Count { get; set; } = 0;
            public string EncounterId { get; set; } = "";
            public string Date { get; set; } = "";
        }

        private class BufferRecord
        {
            public float SizeKB { get; set; } = 0f;
            public int EncounterCount { get; set; } = 0;
            public string Date { get; set; } = "";
        }
        #endregion

        #region Initialization
        private void Init()
        {
            permission.RegisterPermission("thedenanticheat.bypass", this);
            LoadWeaponData();
            UpdateLogPath();
            EnsureLogFileExists();
            LoadViolationStats();
            LoadAdminStats();
        }

        private void LoadWeaponData()
        {
            try
            {
                if (!File.Exists(weaponDataPath))
                {
                    Puts($"Warning: Weapon data file not found at {weaponDataPath}");
                    Puts("Run 'dumpweapon' command to generate weapon data for enhanced detection");
                    return;
                }

                // Check if we need to reload the data (daily refresh)
                var fileInfo = new FileInfo(weaponDataPath);
                if (fileInfo.LastWriteTime <= lastWeaponDataLoad && weaponDatabase.Count > 0)
                    return;

                string jsonContent = File.ReadAllText(weaponDataPath);
                var weaponDump = JsonConvert.DeserializeObject<Dictionary<string, Dictionary<string, object>>>(jsonContent);

                weaponDatabase.Clear();
                int loadedCount = 0;

                foreach (var kvp in weaponDump)
                {
                    try
                    {
                        var weaponJson = kvp.Value;
                        var weapon = new WeaponData
                        {
                            DisplayName = weaponJson.ContainsKey("displayName") ? weaponJson["displayName"].ToString() : "",
                            ShortName = weaponJson.ContainsKey("shortName") ? weaponJson["shortName"].ToString() : kvp.Key,
                            ItemID = weaponJson.ContainsKey("itemID") ? Convert.ToInt32(weaponJson["itemID"]) : 0,
                            YawMin = weaponJson.ContainsKey("yawMin") ? Convert.ToSingle(weaponJson["yawMin"]) : 0f,
                            YawMax = weaponJson.ContainsKey("yawMax") ? Convert.ToSingle(weaponJson["yawMax"]) : 0f,
                            PitchMin = weaponJson.ContainsKey("pitchMin") ? Convert.ToSingle(weaponJson["pitchMin"]) : 0f,
                            PitchMax = weaponJson.ContainsKey("pitchMax") ? Convert.ToSingle(weaponJson["pitchMax"]) : 0f,
                            Aimcone = weaponJson.ContainsKey("aimcone") ? Convert.ToSingle(weaponJson["aimcone"]) : 0f,
                            RPM = weaponJson.ContainsKey("rpm") ? Convert.ToInt32(weaponJson["rpm"]) : 0,
                            IsAutomatic = weaponJson.ContainsKey("isAutomatic") ? Convert.ToBoolean(weaponJson["isAutomatic"]) : false,
                            AmmoType = weaponJson.ContainsKey("ammoType") ? weaponJson["ammoType"].ToString() : "unknown",
                            MagazineSize = weaponJson.ContainsKey("magazineSize") ? Convert.ToInt32(weaponJson["magazineSize"]) : 0,
                            ReloadTime = weaponJson.ContainsKey("reloadTime") ? Convert.ToSingle(weaponJson["reloadTime"]) : 0f
                        };

                        weaponDatabase[kvp.Key] = weapon;
                        loadedCount++;
                    }
                    catch (Exception ex)
                    {
                        Puts($"Error parsing weapon data for {kvp.Key}: {ex.Message}");
                    }
                }

                lastWeaponDataLoad = DateTime.UtcNow;
                Puts($"✅ Loaded weapon data for {loadedCount} weapons from {weaponDataPath}");
            }
            catch (Exception ex)
            {
                Puts($"Error loading weapon data: {ex.Message}");
                Puts("Enhanced recoil detection will be limited without weapon data");
            }
        }

        private void UpdateLogPath()
        {
            currentDate = DateTime.UtcNow.ToString("yyyyMMdd");
            detectionLogPath = $"oxide/logs/thedenproject/detection_log_{currentDate}.csv";
        }

        private void EnsureLogFileExists()
        {
            if (!config.Logging.EnableLogging) return;
            EnsureDetectionLogExists();
        }

        private void EnsureDetectionLogExists()
        {
            string directory = Path.GetDirectoryName(detectionLogPath);
            if (!Directory.Exists(directory))
                Directory.CreateDirectory(directory);

            if (!File.Exists(detectionLogPath))
            {
                var header = "TIMESTAMP,ENCOUNTER_ID,PLAYER_ID,PLAYER_NAME," +
                            "TARGET_ID,TARGET_NAME,VIOLATION_TYPE,WEAPON," +
                            "ENCOUNTER_DURATION,SHOT_COUNT,WAS_DETECTED," +
                            "WEAPON_HAS_DATA,WEAPON_EXPECTED_RECOIL_RANGE,WEAPON_FIRE_INTERVAL," +
                            "WEAPON_RPM,WEAPON_DISPLAY_NAME," +
                            "WEAPON_YAW_MIN,WEAPON_YAW_MAX,WEAPON_PITCH_MIN,WEAPON_PITCH_MAX," +
                            "PATTERN_DETECTION_ENABLED,CONFIG_VARIANCE_THRESHOLD,CONFIG_MAX_ANGLE_CHANGE," +
                            "DETECTION_METHOD,ANGLE_CHANGES_LIST," +
                            "CALCULATED_MEAN,CALCULATED_VARIANCE,CALCULATED_STD_DEV," +
                            "CALCULATED_THRESHOLD,PLAYER_RECOIL_RANGE,PLAYER_MIN_RECOIL," +
                            "PLAYER_MAX_RECOIL,DEVIATION_PERCENTAGE";
                
                File.WriteAllText(detectionLogPath, header + "\n");
                
                if (config.General.DebugMode)
                    Puts($"✅ Detection log initialized: {detectionLogPath}");
            }
        }

        private void LoadViolationStats()
        {
            if (!config.General.EnableViolationStats) return;

            try
            {
                if (!Interface.Oxide.DataFileSystem.ExistsDatafile("thedenproject/violationstats"))
                    return;

                var data = Interface.Oxide.DataFileSystem.ReadObject<Dictionary<string, ViolationStats>>("thedenproject/violationstats");
                violationStatsCache.Clear();

                foreach (var kvp in data)
                {
                    if (ulong.TryParse(kvp.Key, out ulong steamId))
                    {
                        violationStatsCache[steamId] = kvp.Value;
                    }
                }

                if (config.General.DebugMode)
                    Puts($"✅ Loaded violation stats for {violationStatsCache.Count} players");
            }
            catch (Exception ex)
            {
                Puts($"Error loading violation stats: {ex.Message}");
            }
        }

        private void SaveViolationStats()
        {
            if (!config.General.EnableViolationStats || !violationStatsDirty) return;

            try
            {
                var data = new Dictionary<string, ViolationStats>();
                foreach (var kvp in violationStatsCache)
                {
                    data[kvp.Key.ToString()] = kvp.Value;
                }

                Interface.Oxide.DataFileSystem.WriteObject("thedenproject/violationstats", data);
                violationStatsDirty = false;
                lastViolationStatsSave = DateTime.UtcNow;

                if (config.General.DebugMode)
                    Puts($"✅ Saved violation stats for {violationStatsCache.Count} players");
            }
            catch (Exception ex)
            {
                Puts($"Error saving violation stats: {ex.Message}");
            }
        }

        // Violation stats save (moved from old FlushLogBuffer)
        private void SaveViolationStatsIfNeeded()
        {
            if (violationStatsDirty && DateTime.UtcNow - lastViolationStatsSave > TimeSpan.FromSeconds(config.Logging.SaveFrequency))
            {
                SaveViolationStats();
            }
        }

        private void LoadAdminStats()
        {
            try
            {
                if (Interface.Oxide.DataFileSystem.ExistsDatafile("thedenproject/adminstats"))
                {
                    adminStats = Interface.Oxide.DataFileSystem.ReadObject<AdminStats>("thedenproject/adminstats");
                    
                    // Check for date rollover and reset stats if needed
                    CheckAdminStatsDateRollover();
                    
                    if (config.General.DebugMode)
                        Puts($"✅ Loaded admin stats from save file");
                }
                else
                {
                    adminStats = new AdminStats();
                    if (config.General.DebugMode)
                        Puts($"✅ Initialized new admin stats");
                }
            }
            catch (Exception ex)
            {
                Puts($"Error loading admin stats: {ex.Message}");
                adminStats = new AdminStats();
            }
        }

        private void SaveAdminStats()
        {
            if (!adminStatsDirty) return;
            
            try
            {
                adminStats.LastUpdated = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");
                Interface.Oxide.DataFileSystem.WriteObject("thedenproject/adminstats", adminStats);
                adminStatsDirty = false;
                
                if (config.General.DebugMode)
                    Puts($"✅ Saved admin stats");
            }
            catch (Exception ex)
            {
                Puts($"Error saving admin stats: {ex.Message}");
            }
        }

        private void CheckAdminStatsDateRollover()
        {
            var today = DateTime.UtcNow.Date;
            var thisWeekStart = today.AddDays(-(int)today.DayOfWeek + (int)DayOfWeek.Monday);
            
            // Reset daily stats if date changed
            if (adminStats.Daily.Date != today)
            {
                adminStats.Daily = new AdminStatsCategory { Date = today };
                adminStats.Realtime.FlushOperationsToday = 0;
                adminStatsDirty = true;
                
                if (config.General.DebugMode)
                    Puts($"[AdminStats] Daily reset - new date: {today:yyyy-MM-dd}");
            }
            
            // Reset weekly stats if week changed
            if (adminStats.Weekly.Date != thisWeekStart)
            {
                adminStats.Weekly = new AdminStatsCategory { Date = thisWeekStart };
                adminStatsDirty = true;
                
                if (config.General.DebugMode)
                    Puts($"[AdminStats] Weekly reset - new week: {thisWeekStart:yyyy-MM-dd}");
            }
        }

        private void UpdateAdminStats()
        {
            var now = DateTime.UtcNow;
            CheckAdminStatsDateRollover();
            
            // Update realtime stats
            adminStats.Realtime.ActiveEncounters = activeEncounters.Count;
            
            // Calculate encounters per hour
            var pluginUptimeHours = (float)(now - pluginStartTime).TotalHours;
            if (pluginUptimeHours > 0)
            {
                adminStats.Realtime.EncountersPerHour = adminStats.AllTime.TotalEncounters / pluginUptimeHours;
            }
            
            // Calculate detection rate
            if (adminStats.AllTime.TotalEncounters > 0)
            {
                // This would need violation count tracking - simplified for now
                adminStats.Realtime.DetectionRatePercent = 0f; // TODO: Implement violation tracking
            }
            
            // Check for stale encounters and update longest encounter
            foreach (var kvp in activeEncounters)
            {
                var encounter = kvp.Value;
                var encounterAge = (float)(now - encounter.StartTime).TotalSeconds;
                
                // Update longest encounter record
                if (encounterAge > adminStats.AllTime.LongestEncounterSeconds)
                {
                    adminStats.AllTime.LongestEncounterSeconds = encounterAge;
                    adminStatsDirty = true;
                }
                if (encounterAge > adminStats.Daily.LongestEncounterSeconds)
                {
                    adminStats.Daily.LongestEncounterSeconds = encounterAge;
                    adminStatsDirty = true;
                }
                if (encounterAge > adminStats.Weekly.LongestEncounterSeconds)
                {
                    adminStats.Weekly.LongestEncounterSeconds = encounterAge;
                    adminStatsDirty = true;
                }
                
                // Check for stale encounters (>120 seconds)
                if (encounterAge > 120f)
                {
                    adminStats.AllTime.StaleEncounterWarnings++;
                    adminStats.Daily.StaleEncounterWarnings++;
                    adminStats.Weekly.StaleEncounterWarnings++;
                    adminStatsDirty = true;
                    
                    Puts($"WARNING: Stale encounter detected: {kvp.Key} running for {encounterAge:F1} seconds");
                }
            }
            
            // Update max concurrent encounters
            var currentActive = activeEncounters.Count;
            if (currentActive > adminStats.AllTime.MaxConcurrentEncounters)
            {
                adminStats.AllTime.MaxConcurrentEncounters = currentActive;
                adminStatsDirty = true;
            }
            if (currentActive > adminStats.Daily.MaxConcurrentEncounters)
            {
                adminStats.Daily.MaxConcurrentEncounters = currentActive;
                adminStatsDirty = true;
            }
            if (currentActive > adminStats.Weekly.MaxConcurrentEncounters)
            {
                adminStats.Weekly.MaxConcurrentEncounters = currentActive;
                adminStatsDirty = true;
            }
        }

        private void TrackEncounterCompletion(string encounterId, int shotCount)
        {
            var today = DateTime.UtcNow.ToString("yyyy-MM-dd");
            
            // Update total encounters
            adminStats.AllTime.TotalEncounters++;
            adminStats.Daily.TotalEncounters++;
            adminStats.Weekly.TotalEncounters++;
            
            // Track max shots per encounter
            if (shotCount > adminStats.AllTime.MaxShotsPerEncounter.Count)
            {
                adminStats.AllTime.MaxShotsPerEncounter = new ShotRecord
                {
                    Count = shotCount,
                    EncounterId = encounterId,
                    Date = today
                };
            }
            if (shotCount > adminStats.Daily.MaxShotsPerEncounter.Count)
            {
                adminStats.Daily.MaxShotsPerEncounter = new ShotRecord
                {
                    Count = shotCount,
                    EncounterId = encounterId,
                    Date = today
                };
            }
            if (shotCount > adminStats.Weekly.MaxShotsPerEncounter.Count)
            {
                adminStats.Weekly.MaxShotsPerEncounter = new ShotRecord
                {
                    Count = shotCount,
                    EncounterId = encounterId,
                    Date = today
                };
            }
            
            adminStatsDirty = true;
        }

        private void TrackBufferSize(float sizeKB, int encounterCount)
        {
            var today = DateTime.UtcNow.ToString("yyyy-MM-dd");
            
            // Track max buffer size
            if (sizeKB > adminStats.AllTime.MaxBufferSize.SizeKB)
            {
                adminStats.AllTime.MaxBufferSize = new BufferRecord
                {
                    SizeKB = sizeKB,
                    EncounterCount = encounterCount,
                    Date = today
                };
            }
            if (sizeKB > adminStats.Daily.MaxBufferSize.SizeKB)
            {
                adminStats.Daily.MaxBufferSize = new BufferRecord
                {
                    SizeKB = sizeKB,
                    EncounterCount = encounterCount,
                    Date = today
                };
            }
            if (sizeKB > adminStats.Weekly.MaxBufferSize.SizeKB)
            {
                adminStats.Weekly.MaxBufferSize = new BufferRecord
                {
                    SizeKB = sizeKB,
                    EncounterCount = encounterCount,
                    Date = today
                };
            }
            
            adminStatsDirty = true;
        }

        private void TrackLogWrite(float sizeKB)
        {
            // Track largest log write
            if (sizeKB > adminStats.AllTime.LargestLogWriteKB)
            {
                adminStats.AllTime.LargestLogWriteKB = sizeKB;
                adminStatsDirty = true;
            }
            if (sizeKB > adminStats.Daily.LargestLogWriteKB)
            {
                adminStats.Daily.LargestLogWriteKB = sizeKB;
                adminStatsDirty = true;
            }
            if (sizeKB > adminStats.Weekly.LargestLogWriteKB)
            {
                adminStats.Weekly.LargestLogWriteKB = sizeKB;
                adminStatsDirty = true;
            }
            
            // Track flush operations
            adminStats.Realtime.FlushOperationsToday++;
            adminStatsDirty = true;
        }
        #endregion

        #region Oxide Hooks
        private void OnPlayerAttack(BasePlayer attacker, HitInfo info)
        {
            if (attacker == null || info == null || !IsPlayerEligible(attacker)) return;

            // In debug mode, detect all combat. In normal mode, only PvP
            if (!config.General.DebugMode)
            {
                // Only process PvP attacks (target must be a human player)
                if (!(info.HitEntity is BasePlayer pvpTarget) || pvpTarget.IsNpc || pvpTarget.net?.connection == null) return;
            }

            // Get weapon name first to check if it's ranged
            string weaponShortName = GetWeaponShortName(attacker, info);

            // RANGED WEAPONS ONLY: Skip detection for non-ranged weapons
            if (!IsRangedWeapon(weaponShortName))
            {
                return;
            }

            // Get target for logging purposes (can be null in debug mode for non-PvP)
            BasePlayer target = info.HitEntity as BasePlayer;

            // Track encounter-based detection
            if (target != null) // Only track encounters with valid targets
            {
                string encounterId = $"{attacker.userID}_{target.userID}";
                
                if (!activeEncounters.ContainsKey(encounterId))
                {
                    // Create new encounter
                    var encounter = new EncounterData
                    {
                        EncounterID = encounterId,
                        AttackerID = attacker.userID,
                        TargetID = target.userID,
                        StartTime = DateTime.UtcNow,
                        LastShotTime = DateTime.UtcNow,
                        Shots = new List<ShotData>(),
                        WeaponUsage = new Dictionary<string, int>()
                    };
                    
                    activeEncounters[encounterId] = encounter;
                    
                    // Set timeout timer
                    encounter.TimeoutTimer = timer.Once(config.Recoil.EventTimeout, () => CompleteEncounter(encounterId, "timeout"));
                }
                
                var activeEncounter = activeEncounters[encounterId];
                activeEncounter.LastShotTime = DateTime.UtcNow;
                
                // Track weapon usage
                if (!activeEncounter.WeaponUsage.ContainsKey(weaponShortName))
                    activeEncounter.WeaponUsage[weaponShortName] = 0;
                activeEncounter.WeaponUsage[weaponShortName]++;
                
                // Determine primary weapon
                var primaryWeapon = activeEncounter.WeaponUsage.OrderByDescending(w => w.Value).First();
                activeEncounter.PrimaryWeapon = primaryWeapon.Key;
                
                // Create shot data for encounter
                var currentAimAngle = attacker.eyes.rotation.eulerAngles;
                var lastAimAngle = activeEncounter.Shots.LastOrDefault()?.AimAngleAfter ?? currentAimAngle;
                
                var shot = new ShotData
                {
                    AimAngleBefore = lastAimAngle,
                    AimAngleAfter = currentAimAngle,
                    Time = (float)DateTime.UtcNow.Subtract(epoch).TotalSeconds,
                    FromPos = attacker.eyes.position,
                    HitPos = info.HitPositionWorld,
                    WeaponShortName = weaponShortName,
                    IsValidRecoilData = activeEncounter.Shots.Count > 0 // Valid if not first shot
                };
                
                activeEncounter.Shots.Add(shot);
                
                // Reset timeout timer
                activeEncounter.TimeoutTimer?.Destroy();
                activeEncounter.TimeoutTimer = timer.Once(config.Recoil.EventTimeout, () => CompleteEncounter(encounterId, "timeout"));
            }
        }

        private void OnPlayerDisconnected(BasePlayer player, string reason)
        {
            playerData.Remove(player.userID);
            
            // Complete any encounters involving this player
            var encountersToComplete = activeEncounters.Where(kvp => 
                kvp.Value.AttackerID == player.userID || kvp.Value.TargetID == player.userID).ToList();
            
            foreach (var encounter in encountersToComplete)
            {
                CompleteEncounter(encounter.Key, "player_disconnect");
            }
        }

        private void OnEntityDeath(BaseCombatEntity entity, HitInfo info)
        {
            if (!(entity is BasePlayer deadPlayer)) return;
            
            // Complete any encounters where this player was the target
            var encountersToComplete = activeEncounters.Where(kvp => 
                kvp.Value.TargetID == deadPlayer.userID).ToList();
            
            foreach (var encounter in encountersToComplete)
            {
                CompleteEncounter(encounter.Key, "target_death");
            }
        }

        private void Unload()
        {
            try
            {
                // Complete all active encounters before unload - but safely handle if config is null
                if (activeEncounters?.Count > 0)
                {
                    var allEncounters = activeEncounters.Keys.ToList();
                    foreach (var encounterId in allEncounters)
                    {
                        CompleteEncounterSafe(encounterId, "plugin_unload");
                    }
                }

                // Flush detection log buffer safely
                FlushDetectionLogBufferSafe();

                // Save admin stats before unload
                SaveAdminStats();

                // Save violation stats before unload
                if (violationStatsDirty)
                {
                    SaveViolationStats();
                }
            }
            catch (Exception ex)
            {
                Puts($"Error during plugin unload: {ex.Message}");
            }
        }
        #endregion

        #region Encounter Analysis
        private void CompleteEncounter(string encounterId, string reason)
        {
            if (!activeEncounters.ContainsKey(encounterId)) return;

            var encounter = activeEncounters[encounterId];
            
            // Clean up timer
            encounter.TimeoutTimer?.Destroy();
            
            // Get player objects
            var attacker = BasePlayer.FindByID(encounter.AttackerID);
            var target = BasePlayer.FindByID(encounter.TargetID);
            
            // Calculate encounter duration
            var duration = (float)(DateTime.UtcNow - encounter.StartTime).TotalSeconds;
            
            // Track encounter completion for admin stats
            TrackEncounterCompletion(encounterId, encounter.Shots.Count);
            
            // ANALYSIS STARTS HERE - ONLY AT ENCOUNTER COMPLETION
            var violations = new List<string>();
            var detectionData = new DetectionEntry
            {
                Timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                EncounterID = encounterId,
                PlayerID = encounter.AttackerID.ToString(),
                PlayerName = attacker?.displayName ?? "Unknown",
                TargetID = encounter.TargetID.ToString(),
                TargetName = target?.displayName ?? "Unknown",
                ViolationType = "NONE",
                Weapon = encounter.PrimaryWeapon,
                EncounterDuration = duration,
                ShotCount = encounter.Shots.Count,
                WasDetected = false
            };

            // Analyze encounter for violations ONLY NOW
            if (encounter.Shots.Count >= 2 && !string.IsNullOrEmpty(encounter.PrimaryWeapon))
            {
                // Check if weapon data is available
                WeaponData weaponInfo = null;
                bool weaponHasData = weaponDatabase.TryGetValue(encounter.PrimaryWeapon, out weaponInfo);

                detectionData.WeaponHasData = weaponHasData;
                detectionData.WeaponExpectedRecoilRange = weaponInfo?.ExpectedRecoilRange ?? 0f;
                detectionData.WeaponFireInterval = weaponInfo?.FireInterval ?? 0f;
                detectionData.WeaponRPM = weaponInfo?.RPM ?? 0;
                detectionData.WeaponDisplayName = weaponInfo?.DisplayName ?? encounter.PrimaryWeapon;
                detectionData.WeaponYawMin = weaponInfo?.YawMin ?? 0f;
                detectionData.WeaponYawMax = weaponInfo?.YawMax ?? 0f;
                detectionData.WeaponPitchMin = weaponInfo?.PitchMin ?? 0f;
                detectionData.WeaponPitchMax = weaponInfo?.PitchMax ?? 0f;
                detectionData.PatternDetectionEnabled = config.Recoil.EnablePatternDetection;
                detectionData.ConfigVarianceThreshold = config.Recoil.VarianceThreshold;
                detectionData.ConfigMaxAngleChange = config.Recoil.MaxAngleChange;

                // Check each detection type and log separately
                if (config.General.EnableRecoilDetection)
                {
                    var recoilViolation = AnalyzeEncounterRecoil(encounter, weaponInfo);
                    if (recoilViolation != null)
                    {
                        violations.Add(recoilViolation);

                        // Log RECOIL violation separately
                        var recoilData = CreateDetectionEntry(encounter, attacker, target, "RECOIL", weaponInfo);
                        LogDetectionEntry(recoilData);
                    }
                }

                if (config.General.EnableAimbotDetection)
                {
                    var aimbotViolation = AnalyzeEncounterAimbot(encounter);
                    if (aimbotViolation != null)
                    {
                        violations.Add(aimbotViolation);

                        // Log AIMBOT violation separately
                        var aimbotData = CreateDetectionEntry(encounter, attacker, target, "AIMBOT", weaponInfo);
                        LogDetectionEntry(aimbotData);
                    }
                }

                if (config.General.EnableWallhackDetection)
                {
                    var wallhackViolation = AnalyzeEncounterWallhack(encounter);
                    if (wallhackViolation != null)
                    {
                        violations.Add(wallhackViolation);

                        // Log WALLHACK violation separately
                        var wallhackData = CreateDetectionEntry(encounter, attacker, target, "WALLHACK", weaponInfo);
                        LogDetectionEntry(wallhackData);
                    }
                }

                // If no violations, log clean encounter
                if (violations.Count == 0)
                {
                    var cleanData = CreateDetectionEntry(encounter, attacker, target, "NONE", weaponInfo);
                    LogDetectionEntry(cleanData);
                }
            }

            // Console logging for violations only
            if (violations.Count > 0 && config.Logging.EnableConsoleLogging)
            {
                var debugPrefix = config.General.DebugMode ? "[DEBUG] " : "";
                Puts($"{debugPrefix}Violation detected in encounter {encounterId}: {detectionData.PlayerName} vs {detectionData.TargetName} - {string.Join("; ", violations)}");
            }

            // Update violation stats ONLY IF VIOLATIONS FOUND
            if (violations.Count > 0 && config.General.EnableViolationStats && attacker != null)
            {
                // Record each individual violation type
                var violationTypes = new List<string>();

                // Check for recoil violations (both enhanced and legacy)
                if (violations.Any(v => v.Contains("Recoil variance") || v.Contains("Angle change")))
                    violationTypes.Add("RECOIL");

                // Check for aimbot violations
                if (violations.Any(v => v.Contains("Hit ratio") && v.Contains("exceeds threshold")))
                    violationTypes.Add("AIMBOT");

                // Check for wallhack violations
                if (violations.Any(v => v.Contains("Shot through obstacle")))
                    violationTypes.Add("WALLHACK");

                // Record each violation type individually
                foreach (var violationType in violationTypes)
                {
                    UpdateViolationStats(attacker, violationType);
                }

                SaveViolationStatsIfNeeded();
            }

            // Remove encounter from active encounters
            activeEncounters.Remove(encounterId);
        }

        private DetectionEntry CreateDetectionEntry(EncounterData encounter, BasePlayer attacker, BasePlayer target, string violationType, WeaponData weaponInfo)
        {
            var duration = (float)(DateTime.UtcNow - encounter.StartTime).TotalSeconds;

            return new DetectionEntry
            {
                Timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                EncounterID = encounter.EncounterID,
                PlayerID = encounter.AttackerID.ToString(),
                PlayerName = attacker?.displayName ?? "Unknown",
                TargetID = encounter.TargetID.ToString(),
                TargetName = target?.displayName ?? "Unknown",
                ViolationType = violationType,
                Weapon = encounter.PrimaryWeapon,
                EncounterDuration = duration,
                ShotCount = encounter.Shots.Count,
                WasDetected = violationType != "NONE",

                // Weapon database fields
                WeaponHasData = weaponInfo != null,
                WeaponExpectedRecoilRange = weaponInfo?.ExpectedRecoilRange ?? 0f,
                WeaponFireInterval = weaponInfo?.FireInterval ?? 0f,
                WeaponRPM = weaponInfo?.RPM ?? 0,
                WeaponDisplayName = weaponInfo?.DisplayName ?? encounter.PrimaryWeapon,
                WeaponYawMin = weaponInfo?.YawMin ?? 0f,
                WeaponYawMax = weaponInfo?.YawMax ?? 0f,
                WeaponPitchMin = weaponInfo?.PitchMin ?? 0f,
                WeaponPitchMax = weaponInfo?.PitchMax ?? 0f,

                // Configuration fields
                PatternDetectionEnabled = config.Recoil.EnablePatternDetection,
                ConfigVarianceThreshold = config.Recoil.VarianceThreshold,
                ConfigMaxAngleChange = config.Recoil.MaxAngleChange,

                // Detection analysis fields (will be populated later if needed)
                DetectionMethod = "",
                AngleChangesList = "",
                CalculatedMean = 0f,
                CalculatedVariance = 0f,
                CalculatedStdDev = 0f,
                CalculatedThreshold = 0f,
                PlayerRecoilRange = 0f,
                PlayerMinRecoil = 0f,
                PlayerMaxRecoil = 0f,
                DeviationPercentage = 0f
            };
        }

        private void LogDetectionEntry(DetectionEntry detectionData)
        {
            if (!config.Logging.EnableLogging) return;

            var csvLine = detectionData.ToCSV() + "\n";
            detectionLogBuffer.Add(csvLine);

            // Track buffer size for admin stats
            string bufferContent = string.Join("", detectionLogBuffer);
            float bufferSizeKB = Encoding.UTF8.GetByteCount(bufferContent) / 1024f;
            TrackBufferSize(bufferSizeKB, detectionLogBuffer.Count);

            // Check buffer size and flush if needed
            if (bufferSizeKB >= config.Logging.MaxBufferSizeKB)
            {
                FlushDetectionLogBuffer();
            }
            else if (detectionLogBuffer.Count == 1 && detectionLogFlushTimer == null)
            {
                // Start timer for first entry
                detectionLogFlushTimer = timer.Once(config.Logging.SaveFrequency, FlushDetectionLogBuffer);
            }
        }

        private string AnalyzeEncounterRecoil(EncounterData encounter, WeaponData weaponInfo)
        {
            var weaponShots = encounter.Shots
                .Where(s => s.WeaponShortName == encounter.PrimaryWeapon && s.IsValidRecoilData)
                .ToList();

            if (weaponShots.Count < 2) return null;

            // Calculate recoil pattern variance
            var angleChanges = new List<float>();
            for (int i = 1; i < weaponShots.Count; i++)
            {
                var prevShot = weaponShots[i - 1];
                var currentShot = weaponShots[i];
                var angleChange = Vector3.Angle(prevShot.AimAngleBefore, currentShot.AimAngleAfter);
                angleChanges.Add(angleChange);
            }

            if (angleChanges.Count < 1) return null;

            if (config.Recoil.EnablePatternDetection && weaponInfo != null)
            {
                // Enhanced pattern detection
                float mean = angleChanges.Average();
                float variance = angleChanges.Select(x => (x - mean) * (x - mean)).Average();
                float stdDev = Mathf.Sqrt(variance);

                float expectedRecoilRange = weaponInfo.ExpectedRecoilRange;
                float minimumAcceptableVariance = expectedRecoilRange * config.Recoil.VarianceThreshold;

                if (stdDev < minimumAcceptableVariance)
                {
                    float playerMinRecoil = angleChanges.Min();
                    float playerMaxRecoil = angleChanges.Max();
                    float playerRecoilRange = playerMaxRecoil - playerMinRecoil;
                    float deviationPercentage = expectedRecoilRange > 0 ? ((stdDev - expectedRecoilRange) / expectedRecoilRange) * 100f : 0f;

                    return $"Recoil variance {stdDev:F3} below minimum threshold {minimumAcceptableVariance:F3} (expected range: {expectedRecoilRange:F3}, player range: {playerRecoilRange:F3}, deviation: {deviationPercentage:F1}%, min required: {config.Recoil.VarianceThreshold * 100:F0}%, shots: {angleChanges.Count}, mean: {mean:F3})";
                }
            }
            else
            {
                // Legacy detection
                foreach (var angleChange in angleChanges)
                {
                    if (angleChange < config.Recoil.MaxAngleChange && angleChange >= 0.01f)
                    {
                        return $"Angle change {angleChange:F2} below threshold {config.Recoil.MaxAngleChange} for {encounter.PrimaryWeapon}";
                    }
                }
            }

            return null;
        }

        private string AnalyzeEncounterAimbot(EncounterData encounter)
        {
            if (encounter.Shots.Count < config.Aimbot.MinShots) return null;

            int hits = encounter.Shots.Count(s => Vector3.Distance(s.HitPos, Vector3.zero) > 0.1f); // Count shots that actually hit
            float hitRatio = (float)hits / encounter.Shots.Count;

            if (hitRatio > config.Aimbot.MaxHitRatio)
            {
                return $"Hit ratio {hitRatio:F2} exceeds threshold {config.Aimbot.MaxHitRatio} in encounter";
            }

            return null;
        }

        private string AnalyzeEncounterWallhack(EncounterData encounter)
        {
            foreach (var shot in encounter.Shots)
            {
                RaycastHit hit;
                var direction = (shot.HitPos - shot.FromPos).normalized;
                if (Physics.Raycast(shot.FromPos, direction, out hit, Vector3.Distance(shot.FromPos, shot.HitPos), LayerMask.GetMask("Terrain", "World", "Construction")))
                {
                    if (hit.collider != null)
                    {
                        return $"Shot through obstacle at {hit.collider.name} with {shot.WeaponShortName}";
                    }
                }
            }
            return null;
        }
        #endregion

        #region Cheat Detection
        private void CheckAimbot(BasePlayer player, BasePlayer target, PlayerData data)
        {
            // Add the minimum shots check here!
            if (data.ShotsFired < config.Aimbot.MinShots) return;

            float hitRatio = (float)data.ShotsHit / data.ShotsFired;
            if (hitRatio > config.Aimbot.MaxHitRatio)
            {
                string weapon = data.ShotHistory.LastOrDefault()?.WeaponShortName ?? "unknown";
                LogViolation(player, target, "Aimbot", weapon, $"PvP hit ratio {hitRatio:F2} exceeds threshold {config.Aimbot.MaxHitRatio}");
            }
        }

        private void CheckRecoil(BasePlayer player, BasePlayer target, PlayerData data, ShotData shot)
        {
            // Check if weapon data is available
            WeaponData weaponInfo = null;
            bool weaponHasData = weaponDatabase.TryGetValue(shot.WeaponShortName, out weaponInfo);

            if (config.Recoil.EnablePatternDetection && weaponInfo != null)
            {
                // Enhanced detection ONLY (weapon data available)
                CheckRecoilPattern(player, target, data, shot, weaponInfo);
            }
            else
            {
                // Legacy detection ONLY (no weapon data OR pattern detection disabled)
                CheckLegacyRecoil(player, target, data, shot, weaponInfo);
            }
        }

        private void CheckRecoilPattern(BasePlayer player, BasePlayer target, PlayerData data, ShotData shot, WeaponData weaponInfo)
        {
            // Encounter-based detection: analyze any amount of data available
            var weaponShots = data.ShotHistory
                .Where(s => s.WeaponShortName == shot.WeaponShortName && s.IsValidRecoilData)
                .OrderByDescending(s => s.Time)
                .Take(50) // Reasonable limit for analysis window
                .OrderBy(s => s.Time)
                .ToList();

            // Need at least 2 shots to calculate angle changes
            if (weaponShots.Count < 2) return;

            // Calculate recoil pattern variance
            var angleChanges = new List<float>();
            for (int i = 1; i < weaponShots.Count; i++)
            {
                var prevShot = weaponShots[i - 1];
                var currentShot = weaponShots[i];
                var angleChange = Vector3.Angle(prevShot.AimAngleBefore, currentShot.AimAngleAfter);
                
                // Include ALL angle changes - zero changes are evidence of cheating, not measurement errors
                angleChanges.Add(angleChange);
            }

            // Need at least 1 angle change to analyze
            if (angleChanges.Count < 1) return;

            // Calculate standard deviation of angle changes
            float mean = angleChanges.Average();
            float variance = angleChanges.Select(x => (x - mean) * (x - mean)).Average();
            float stdDev = Mathf.Sqrt(variance);

            // Calculate minimum acceptable variance based on weapon's natural recoil
            float expectedRecoilRange = weaponInfo.ExpectedRecoilRange;
            float minimumAcceptableVariance = expectedRecoilRange * config.Recoil.VarianceThreshold;

            // Flag if recoil is too consistent (low variance) - detect all low variance patterns
            if (stdDev < minimumAcceptableVariance)
            {
                // Calculate player's actual recoil range for comparison
                float playerMinRecoil = angleChanges.Min();
                float playerMaxRecoil = angleChanges.Max();
                float playerRecoilRange = playerMaxRecoil - playerMinRecoil;

                // Calculate deviation percentage from expected
                float deviationPercentage = expectedRecoilRange > 0 ? ((stdDev - expectedRecoilRange) / expectedRecoilRange) * 100f : 0f;

                LogViolation(player, target, "RecoilPattern", shot.WeaponShortName,
                    $"Recoil variance {stdDev:F3} below minimum threshold {minimumAcceptableVariance:F3} (expected range: {expectedRecoilRange:F3}, player range: {playerRecoilRange:F3}, deviation: {deviationPercentage:F1}%, min required: {config.Recoil.VarianceThreshold * 100:F0}%, shots: {angleChanges.Count}, mean: {mean:F3})");
            }
        }


        private void CheckLegacyRecoil(BasePlayer player, BasePlayer target, PlayerData data, ShotData shot, WeaponData weaponInfo)
        {
            float minInterval = weaponInfo?.FireInterval ?? 0.15f;

            var recentShots = data.ShotHistory
                .Where(s => s.WeaponShortName == shot.WeaponShortName && s.Time > shot.Time - minInterval && s.IsValidRecoilData)
                .OrderBy(s => s.Time)
                .ToList();

            // Need at least 2 shots to compare angles
            if (recentShots.Count < 2) return;

            for (int i = 1; i < recentShots.Count; i++)
            {
                var prevShot = recentShots[i - 1];
                var angleChange = Vector3.Angle(prevShot.AimAngleBefore, recentShots[i].AimAngleAfter);
                
                // Skip impossible 0.00 values
                if (angleChange < 0.01f) continue;
                
                if (angleChange < config.Recoil.MaxAngleChange)
                {
                    LogViolation(player, target, "NoRecoilLegacy", shot.WeaponShortName, 
                        $"Angle change {angleChange:F2} below threshold {config.Recoil.MaxAngleChange} for {shot.WeaponShortName}");
                    break;
                }
            }
        }

        private void CheckWallhack(BasePlayer player, BasePlayer target, ShotData shot)
        {
            RaycastHit hit;
            var direction = (shot.HitPos - shot.FromPos).normalized;
            if (Physics.Raycast(shot.FromPos, direction, out hit, Vector3.Distance(shot.FromPos, shot.HitPos), LayerMask.GetMask("Terrain", "World", "Construction")))
            {
                if (hit.collider != null && hit.collider.gameObject != target.gameObject)
                {
                    LogViolation(player, target, "Wallhack", shot.WeaponShortName, $"PvP shot through obstacle at {hit.collider.name} with {shot.WeaponShortName}");
                }
            }
        }
        #endregion

        #region Logging
        private void LogViolation(BasePlayer player, BasePlayer target, string type, string weapon, string details)
        {
            if (!playerData.ContainsKey(player.userID))
                playerData[player.userID] = new PlayerData();

            var violation = new Violation
            {
                Type = type,
                Time = DateTime.UtcNow.Subtract(epoch).TotalSeconds,
                Weapon = weapon,
                Details = details
            };
            playerData[player.userID].Violations.Add(violation);

            var targetID = target != null ? target.userID.ToString() : "N/A";
            var targetName = target != null ? target.displayName : "N/A";
            
            // Add debug mode indicator to details
            var debugPrefix = config.General.DebugMode ? "[DEBUG] " : "";
            var modifiedDetails = debugPrefix + details;
            
            // Console logging controlled by config
            if (config.Logging.EnableConsoleLogging)
            {
                Puts($"{debugPrefix}Violation detected: {player.displayName} ({player.userID}) vs {targetName} ({targetID}) - {type} ({weapon}): {details}");
            }

            // Log to detection log buffer
            if (config.Logging.EnableLogging)
            {
                var weaponInfo = weaponDatabase.ContainsKey(weapon) ? weaponDatabase[weapon] : null;
                var detectionEntry = new DetectionEntry
                {
                    Timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                    EncounterID = "N/A", // No encounter system in player-based detection
                    PlayerID = player.UserIDString,
                    PlayerName = player.displayName,
                    TargetID = targetID,
                    TargetName = targetName,
                    ViolationType = type,
                    Weapon = weapon,
                    EncounterDuration = 0f, // No encounter system
                    ShotCount = playerData[player.userID].ShotsFired,
                    WasDetected = true,

                    // Weapon data (if available)
                    WeaponHasData = weaponInfo != null,
                    WeaponExpectedRecoilRange = weaponInfo?.ExpectedRecoilRange ?? 0f,
                    WeaponFireInterval = weaponInfo?.FireInterval ?? 0f,
                    WeaponRPM = weaponInfo?.RPM ?? 0,
                    WeaponDisplayName = weaponInfo?.DisplayName ?? weapon,
                    WeaponYawMin = weaponInfo?.YawMin ?? 0f,
                    WeaponYawMax = weaponInfo?.YawMax ?? 0f,
                    WeaponPitchMin = weaponInfo?.PitchMin ?? 0f,
                    WeaponPitchMax = weaponInfo?.PitchMax ?? 0f,

                    // Config data
                    PatternDetectionEnabled = config.Recoil.EnablePatternDetection,
                    ConfigVarianceThreshold = config.Recoil.VarianceThreshold,
                    ConfigMaxAngleChange = config.Recoil.MaxAngleChange,
                    DetectionMethod = weaponInfo != null && config.Recoil.EnablePatternDetection ? "Enhanced" : "Legacy",

                    // Default values for fields not applicable to simple violation logging
                    AngleChangesList = "",
                    CalculatedMean = 0f,
                    CalculatedVariance = 0f,
                    CalculatedStdDev = 0f,
                    CalculatedThreshold = 0f,
                    PlayerRecoilRange = 0f,
                    PlayerMinRecoil = 0f,
                    PlayerMaxRecoil = 0f,
                    DeviationPercentage = 0f
                };

                var csvLine = detectionEntry.ToCSV() + "\n";
                detectionLogBuffer.Add(csvLine);

                // Check buffer size and flush if needed
                string bufferContent = string.Join("", detectionLogBuffer);
                float bufferSizeKB = Encoding.UTF8.GetByteCount(bufferContent) / 1024f;

                if (bufferSizeKB >= config.Logging.MaxBufferSizeKB)
                {
                    FlushDetectionLogBuffer();
                }
                else if (detectionLogBuffer.Count == 1 && detectionLogFlushTimer == null)
                {
                    // Start timer for first entry
                    detectionLogFlushTimer = timer.Once(config.Logging.SaveFrequency, FlushDetectionLogBuffer);
                }
            }

            // Update violation stats (lightweight)
            if (config.General.EnableViolationStats)
            {
                UpdateViolationStats(player, type);
                SaveViolationStatsIfNeeded();
            }
        }

        private void UpdateViolationStats(BasePlayer player, string violationType)
        {
            var steamId = player.userID;
            var now = DateTime.UtcNow;

            // Lazy initialization - only create entry when first violation occurs
            if (!violationStatsCache.ContainsKey(steamId))
            {
                violationStatsCache[steamId] = new ViolationStats
                {
                    PlayerName = player.displayName,
                    TotalViolations = 0,
                    FirstViolation = now,
                    LastViolation = now,
                    ViolationsToday = 0,
                    CurrentStatsDate = now.Date
                };
            }

            var stats = violationStatsCache[steamId];
            
            // Check for date rollover and reset daily stats if needed
            if (now.Date != stats.CurrentStatsDate)
            {
                stats.ViolationsToday = 0;
                stats.ViolationsByTypeToday.Clear();
                stats.CurrentStatsDate = now.Date;
                
                if (config.General.DebugMode)
                {
                    Puts($"[ViolationStats] Daily reset for {player.displayName} - new date: {now.Date:yyyy-MM-dd}");
                }
            }
            
            // Update basic stats
            stats.PlayerName = player.displayName; // Keep name current
            stats.TotalViolations++;
            stats.LastViolation = now;

            // Update violation type tracking (all-time)
            if (!stats.ViolationsByType.ContainsKey(violationType))
                stats.ViolationsByType[violationType] = 0;
            stats.ViolationsByType[violationType]++;

            // Update daily stats
            stats.ViolationsToday++;
            if (!stats.ViolationsByTypeToday.ContainsKey(violationType))
                stats.ViolationsByTypeToday[violationType] = 0;
            stats.ViolationsByTypeToday[violationType]++;

            // Mark stats as dirty for save
            violationStatsDirty = true;

            if (config.General.DebugMode)
            {
                Puts($"[ViolationStats] {player.displayName}: {violationType} (Total: {stats.TotalViolations}, Today: {stats.ViolationsToday})");
            }
        }

        private void FlushDetectionLogBuffer()
        {
            if (detectionLogBuffer.Count == 0) return;

            try
            {
                // Check if date has changed
                string newDate = DateTime.UtcNow.ToString("yyyyMMdd");
                if (newDate != currentDate)
                {
                    UpdateLogPath();
                    EnsureLogFileExists();
                }

                // Calculate size before writing for admin stats
                string bufferContent = string.Join("", detectionLogBuffer);
                float writeSize = Encoding.UTF8.GetByteCount(bufferContent) / 1024f;

                File.AppendAllText(detectionLogPath, bufferContent);
                detectionLogBuffer.Clear();
                
                // Track log write size for admin stats
                TrackLogWrite(writeSize);
                
                if (config.General.DebugMode)
                    Puts($"[DETECTION_LOG] Flushed {writeSize:F1}KB detection buffer to {Path.GetFileName(detectionLogPath)}");
            }
            catch (Exception ex)
            {
                Puts($"Error flushing detection log buffer: {ex.Message}");
            }
            finally
            {
                detectionLogFlushTimer?.Destroy();
                detectionLogFlushTimer = null;
            }
        }

        // Safe versions for use during plugin unload when config might be null
        private void CompleteEncounterSafe(string encounterId, string reason)
        {
            try
            {
                if (!activeEncounters.ContainsKey(encounterId)) return;

                var encounter = activeEncounters[encounterId];
                
                // Clean up timer
                encounter.TimeoutTimer?.Destroy();
                
                // Get player objects (may be null during unload)
                var attacker = BasePlayer.FindByID(encounter.AttackerID);
                var target = BasePlayer.FindByID(encounter.TargetID);
                
                // Calculate encounter duration
                var duration = (float)(DateTime.UtcNow - encounter.StartTime).TotalSeconds;
                
                // Track encounter completion for admin stats (if available)
                if (adminStats != null)
                {
                    TrackEncounterCompletion(encounterId, encounter.Shots.Count);
                }
                
                // Simple logging during unload - no complex analysis
                var detectionData = new DetectionEntry
                {
                    Timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                    EncounterID = encounterId ?? "",
                    PlayerID = encounter.AttackerID.ToString(),
                    PlayerName = attacker?.displayName ?? "Unknown",
                    TargetID = encounter.TargetID.ToString(),
                    TargetName = target?.displayName ?? "Unknown",
                    ViolationType = "NONE",
                    Weapon = encounter.PrimaryWeapon ?? "",
                    EncounterDuration = duration,
                    ShotCount = encounter.Shots?.Count ?? 0,
                    WasDetected = false,

                    // Safe defaults for all other fields
                    WeaponHasData = false,
                    WeaponExpectedRecoilRange = 0f,
                    WeaponFireInterval = 0f,
                    WeaponRPM = 0,
                    WeaponDisplayName = encounter.PrimaryWeapon ?? "",
                    WeaponYawMin = 0f,
                    WeaponYawMax = 0f,
                    WeaponPitchMin = 0f,
                    WeaponPitchMax = 0f,
                    PatternDetectionEnabled = false,
                    ConfigVarianceThreshold = 0f,
                    ConfigMaxAngleChange = 0f,
                    DetectionMethod = "",
                    AngleChangesList = "",
                    CalculatedMean = 0f,
                    CalculatedVariance = 0f,
                    CalculatedStdDev = 0f,
                    CalculatedThreshold = 0f,
                    PlayerRecoilRange = 0f,
                    PlayerMinRecoil = 0f,
                    PlayerMaxRecoil = 0f,
                    DeviationPercentage = 0f
                };

                // Only log if config allows and buffer exists
                if (config?.Logging?.EnableLogging == true && detectionLogBuffer != null)
                {
                    var csvLine = detectionData.ToCSV() + "\n";
                    detectionLogBuffer.Add(csvLine);
                }

                // Remove encounter from active encounters
                activeEncounters.Remove(encounterId);
            }
            catch (Exception ex)
            {
                Puts($"Error during safe encounter completion: {ex.Message}");
                // Still try to remove the encounter to prevent memory leaks
                try
                {
                    if (activeEncounters.ContainsKey(encounterId))
                    {
                        activeEncounters[encounterId].TimeoutTimer?.Destroy();
                        activeEncounters.Remove(encounterId);
                    }
                }
                catch
                {
                    // Silent fail for cleanup
                }
            }
        }

        private void FlushDetectionLogBufferSafe()
        {
            try
            {
                if (detectionLogBuffer?.Count == 0) return;

                // Calculate size before writing for admin stats
                string bufferContent = string.Join("", detectionLogBuffer);
                float writeSize = Encoding.UTF8.GetByteCount(bufferContent) / 1024f;

                // Only write if we have a valid path
                if (!string.IsNullOrEmpty(detectionLogPath))
                {
                    File.AppendAllText(detectionLogPath, bufferContent);
                }

                detectionLogBuffer.Clear();
                
                // Track log write size for admin stats (if available)
                if (adminStats != null)
                {
                    TrackLogWrite(writeSize);
                }
                
                Puts($"[DETECTION_LOG] Safely flushed {writeSize:F1}KB detection buffer during unload");
            }
            catch (Exception ex)
            {
                Puts($"Error during safe detection log buffer flush: {ex.Message}");
            }
            finally
            {
                detectionLogFlushTimer?.Destroy();
                detectionLogFlushTimer = null;
            }
        }
        #endregion


        #region Console Commands
        [ConsoleCommand("clearstats")]
        private void ClearStatsCommand(ConsoleSystem.Arg arg)
        {
            // Admin permission check
            if (arg.Connection != null && arg.Connection.authLevel < 2)
            {
                arg.ReplyWith("❌ Access denied. Admin privileges required.");
                return;
            }
            
            // Get count before clearing
            int clearedCount = violationStatsCache.Count;
            
            // Clear in-memory cache
            violationStatsCache.Clear();
            
            // Delete the data file if it exists
            try
            {
                if (Interface.Oxide.DataFileSystem.ExistsDatafile("thedenproject/violationstats"))
                {
                    string dataFilePath = Path.Combine(Interface.Oxide.DataDirectory, "thedenproject", "violationstats.json");
                    if (File.Exists(dataFilePath))
                    {
                        File.Delete(dataFilePath);
                    }
                }
            }
            catch (Exception ex)
            {
                Puts($"Warning: Could not delete violation stats file: {ex.Message}");
            }
            
            // Reset dirty flag
            violationStatsDirty = false;
            
            // Show confirmation
            arg.ReplyWith($"✅ Cleared violation stats for {clearedCount} players.");
            
            // Debug log if enabled
            if (config.General.DebugMode)
            {
                Puts($"[DEBUG] Violation stats cleared by {(arg.Connection?.username ?? "Server Console")} - {clearedCount} player records removed");
            }
        }

        [ConsoleCommand("detection_admin_stats")]
        private void AdminStatsCommand(ConsoleSystem.Arg arg)
        {
            // Admin permission check
            if (arg.Connection != null && arg.Connection.authLevel < 2)
            {
                arg.ReplyWith("❌ Access denied. Admin privileges required.");
                return;
            }
            
            // Update stats before displaying
            UpdateAdminStats();
            
            var sb = new StringBuilder();
            sb.AppendLine("=== DETECTION SYSTEM ADMIN STATS ===");
            sb.AppendLine();
            
            // Plugin uptime
            var uptime = DateTime.UtcNow - pluginStartTime;
            sb.AppendLine($"Plugin Uptime: {uptime.Days}d {uptime.Hours}h {uptime.Minutes}m");
            sb.AppendLine($"Last Updated: {adminStats.LastUpdated}");
            sb.AppendLine();
            
            // Shot Count Records
            sb.AppendLine("Shot Count Records:");
            sb.AppendLine($"  All-Time Record: {adminStats.AllTime.MaxShotsPerEncounter.Count} shots");
            if (!string.IsNullOrEmpty(adminStats.AllTime.MaxShotsPerEncounter.EncounterId))
                sb.AppendLine($"    Encounter: {adminStats.AllTime.MaxShotsPerEncounter.EncounterId} on {adminStats.AllTime.MaxShotsPerEncounter.Date}");
            sb.AppendLine($"  Today's Peak: {adminStats.Daily.MaxShotsPerEncounter.Count} shots");
            sb.AppendLine($"  This Week's Peak: {adminStats.Weekly.MaxShotsPerEncounter.Count} shots");
            sb.AppendLine();
            
            // Memory Buffer Statistics
            sb.AppendLine("Memory Buffer Statistics:");
            sb.AppendLine($"  All-Time Peak: {adminStats.AllTime.MaxBufferSize.SizeKB:F1} KB ({adminStats.AllTime.MaxBufferSize.EncounterCount} encounters)");
            sb.AppendLine($"  Today's Peak: {adminStats.Daily.MaxBufferSize.SizeKB:F1} KB ({adminStats.Daily.MaxBufferSize.EncounterCount} encounters)");
            sb.AppendLine($"  This Week's Peak: {adminStats.Weekly.MaxBufferSize.SizeKB:F1} KB ({adminStats.Weekly.MaxBufferSize.EncounterCount} encounters)");
            sb.AppendLine();
            
            // System Load Metrics
            sb.AppendLine("System Load Metrics:");
            sb.AppendLine($"  Total Encounters (All-Time): {adminStats.AllTime.TotalEncounters:N0}");
            sb.AppendLine($"  Total Encounters (Today): {adminStats.Daily.TotalEncounters:N0}");
            sb.AppendLine($"  Total Encounters (This Week): {adminStats.Weekly.TotalEncounters:N0}");
            sb.AppendLine($"  Encounters per Hour: {adminStats.Realtime.EncountersPerHour:F1}");
            sb.AppendLine($"  Peak Concurrent (All-Time): {adminStats.AllTime.MaxConcurrentEncounters}");
            sb.AppendLine($"  Peak Concurrent (Today): {adminStats.Daily.MaxConcurrentEncounters}");
            sb.AppendLine($"  Peak Concurrent (This Week): {adminStats.Weekly.MaxConcurrentEncounters}");
            sb.AppendLine();
            
            // File I/O Performance
            sb.AppendLine("File I/O Performance:");
            sb.AppendLine($"  Largest Log Write (All-Time): {adminStats.AllTime.LargestLogWriteKB:F1} KB");
            sb.AppendLine($"  Largest Log Write (Today): {adminStats.Daily.LargestLogWriteKB:F1} KB");
            sb.AppendLine($"  Largest Log Write (This Week): {adminStats.Weekly.LargestLogWriteKB:F1} KB");
            sb.AppendLine($"  Flush Operations Today: {adminStats.Realtime.FlushOperationsToday}");
            sb.AppendLine();
            
            // Active Encounters
            sb.AppendLine("Active Encounters:");
            sb.AppendLine($"  Current Active: {adminStats.Realtime.ActiveEncounters}");
            sb.AppendLine($"  Longest Encounter (All-Time): {adminStats.AllTime.LongestEncounterSeconds:F1} seconds");
            sb.AppendLine($"  Longest Encounter (Today): {adminStats.Daily.LongestEncounterSeconds:F1} seconds");
            sb.AppendLine($"  Longest Encounter (This Week): {adminStats.Weekly.LongestEncounterSeconds:F1} seconds");
            sb.AppendLine();
            
            // Stale Encounter Warnings
            sb.AppendLine("Stale Encounter Warnings (>120s):");
            sb.AppendLine($"  All-Time: {adminStats.AllTime.StaleEncounterWarnings}");
            sb.AppendLine($"  Today: {adminStats.Daily.StaleEncounterWarnings}");
            sb.AppendLine($"  This Week: {adminStats.Weekly.StaleEncounterWarnings}");
            
            arg.ReplyWith(sb.ToString());
            
            // Save stats after displaying
            SaveAdminStats();
        }
        #endregion

        #region Helper Methods
        private bool IsPlayerEligible(BasePlayer player)
        {
            return player != null && !player.IsNpc && player.net?.connection != null && !permission.UserHasPermission(player.UserIDString, "thedenanticheat.bypass");
        }

        private bool IsRangedWeapon(string weaponShortName)
        {
            if (string.IsNullOrEmpty(weaponShortName) || weaponShortName == "unknown")
                return false;

            // Define ranged weapons by category
            var rangedWeapons = new HashSet<string>
            {
                // Assault Rifles
                "rifle.ak", "rifle.lr300", "rifle.m39", "rifle.semiauto",
                
                // SMGs
                "smg.2", "smg.thompson", "smg.mp5",
                
                // Pistols
                "pistol.m92", "pistol.python", "pistol.revolver", "pistol.semiauto",
                "pistol.eoka", "pistol.nailgun",
                
                // Shotguns
                "shotgun.pump", "shotgun.waterpipe", "shotgun.double", "shotgun.m4",
                "shotgun.spas12",
                
                // Sniper Rifles
                "rifle.bolt", "rifle.l96",
                
                // Crossbows and Bows
                "crossbow", "bow.hunting", "bow.compound",
                
                // Launchers
                "rocket.launcher", "multiplegrenadelauncher",
                
                // Machine Guns
                "lmg.m249",
                
                // Special Ranged
                "hmlmg", // Heavy Machine Gun
                "minigun", // Minigun
                
                // Legacy/Custom
                "rifle.m4" // M4 if exists in custom servers
            };

            return rangedWeapons.Contains(weaponShortName.ToLower());
        }

        private string GetWeaponShortName(BasePlayer attacker, HitInfo info)
        {
            // Method 1: Try to get from HitInfo.Weapon
            if (info.Weapon?.GetItem()?.info?.shortname != null)
                return info.Weapon.GetItem().info.shortname;

            // Method 2: Try to get from HitInfo.WeaponPrefab
            if (info.WeaponPrefab?.GetItem()?.info?.shortname != null)
                return info.WeaponPrefab.GetItem().info.shortname;

            // Method 3: Try to get from attacker's held entity
            var heldEntity = attacker.GetHeldEntity();
            if (heldEntity?.GetItem()?.info?.shortname != null)
                return heldEntity.GetItem().info.shortname;

            // Method 4: Try to get from BaseProjectile component
            var weapon = heldEntity?.GetComponent<BaseProjectile>();
            if (weapon?.GetItem()?.info?.shortname != null)
                return weapon.GetItem().info.shortname;

            // Method 5: Check if it's a melee weapon (but we'll filter these out anyway)
            var meleeWeapon = heldEntity?.GetComponent<BaseMelee>();
            if (meleeWeapon?.GetItem()?.info?.shortname != null)
                return meleeWeapon.GetItem().info.shortname;

            return "unknown";
        }
        #endregion
    }
}
